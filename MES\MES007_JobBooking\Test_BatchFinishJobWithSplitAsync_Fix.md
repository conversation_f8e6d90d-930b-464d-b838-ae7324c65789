# BatchFinishJobWithSplitAsync 修复说明

## 问题描述
在使用 `BatchFinishJobWithSplitAsync` 方法进行拆分完工时，会在下游工序产生计划产出数量为0的工艺。

### 问题场景
1. 总量3000
2. 先使用 `BatchFinishJobWithSplitAsync` 方法拆分完工1500
3. 再使用 `BatchFinishJobWithSplitAsync` 方法拆分完工1500
4. 结果：下游工序产生了一个计划数量为0的工艺

## 问题根因
在 `BatchFinishJobWithSplitAsync` 方法中存在双重扣减问题：

1. **第一次扣减**：在部分拆分逻辑中（第5379行），从原始排程的 `FPLAN_QTY` 中减去拆分数量
   ```csharp
   originalSchedule.FPLAN_QTY -= totalQtyToSplit;
   ```

2. **第二次扣减**：在创建下游工序时（第7190行和第6037行），再次从下游父排程的 `FPLAN_QTY` 中减去数量
   ```csharp
   parentsToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID].FPLAN_QTY -= qty;
   ```

这导致下游父排程被重复扣减，最终可能变成0或负数。

## 修复方案

### 1. 修复 `BatchCreateDownstreamTasksWithPassQtyAsync` 方法
在扣减父排程数量前，增加数量检查和复用逻辑：

```csharp
// 检查父排程是否有足够的数量可以扣减
if (parentSchedule.FPLAN_QTY < qty)
{
    // 如果父排程数量不足，只创建剩余数量的子排程
    qty = parentSchedule.FPLAN_QTY;

    // 如果剩余数量为0或负数，跳过创建
    if (qty <= 0)
    {
        continue;
    }
}

// 检查是否应该直接复用父排程
if (parentSchedule.FPLAN_QTY == qty)
{
    // 数量完全匹配，直接复用父排程
    parentSchedule.FLOT_NO = splitSchedule.FLOT_NO;
    parentSchedule.FSOURCE_JOB_BOOKING_ID = splitSchedule.FCRAFT_SCHEDULE_ID;
    parentSchedule.FREMARK = $"由拆分排程 {splitSchedule.FCRAFT_SCHEDULE_NO} 生成的下游任务";
    // 不需要扣减数量，直接复用
}
else
{
    // 部分数量，创建子排程并扣减父排程数量
    // ...创建子排程逻辑...
    parentsToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID].FPLAN_QTY -= qty;
}
```

### 2. 修复 `CreateDownstreamTasksAsync` 方法
同样增加数量检查和复用逻辑：

```csharp
// 检查父排程是否有足够的数量可以扣减
var actualQty = qty;
if (existingDownstreamSchedule.FPLAN_QTY < qty)
{
    // 如果父排程数量不足，只创建剩余数量的子排程
    actualQty = existingDownstreamSchedule.FPLAN_QTY;

    // 如果剩余数量为0或负数，跳过创建
    if (actualQty <= 0)
    {
        return downstreamTasks;
    }
}

// 检查是否应该直接复用父排程
if (existingDownstreamSchedule.FPLAN_QTY == actualQty)
{
    // 数量完全匹配，直接复用父排程
    existingDownstreamSchedule.FLOT_NO = splitSchedule.FLOT_NO;
    existingDownstreamSchedule.FSOURCE_JOB_BOOKING_ID = splitSchedule.FCRAFT_SCHEDULE_ID;
    existingDownstreamSchedule.FREMARK = $"由拆分排程 {splitSchedule.FCRAFT_SCHEDULE_NO} 生成的下游任务";
    // 不需要扣减数量，直接复用
}
else
{
    // 部分数量，创建子排程并扣减父排程数量
    // ...创建子排程逻辑...
    existingDownstreamSchedule.FPLAN_QTY -= actualQty;
}
```

## 修复效果
修复后，当父排程数量不足时：
1. 不会创建计划数量为0的下游工艺
2. 只会创建剩余可用数量的下游工艺
3. 避免了重复扣减导致的数量异常
4. **优化：当拆分数量正好等于父排程剩余数量时，直接复用父排程，避免不必要的子排程创建**

## 优化亮点
### 智能复用父排程
当拆分的数量正好等于下游父排程的剩余数量时：
- **不创建新的子排程**，直接复用父排程
- **更新父排程的批号、来源等信息**，保持数据关联
- **避免数据冗余**，减少不必要的记录
- **提高性能**，减少数据库操作

### 适用场景
1. **完全消耗场景**：拆分数量 = 父排程剩余数量
2. **部分消耗场景**：拆分数量 < 父排程剩余数量（创建子排程）
3. **数量不足场景**：拆分数量 > 父排程剩余数量（只处理剩余数量）

## 测试建议
1. 测试总量3000，分两次各拆分1500的场景（完全消耗）
2. 测试总量3000，分三次各拆分1000的场景（部分消耗）
3. 测试总量3000，拆分3500的场景（数量不足）
4. 验证下游工序不再出现计划数量为0的工艺
5. **验证完全消耗时直接复用父排程，不创建子排程**
